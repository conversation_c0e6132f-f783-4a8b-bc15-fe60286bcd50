// import 'package:renvo_app/features/my_order/controller.dart';

// class MyOrderModel {
//   final int id;
//   final String title;
//   final OrderStatus status;

//   MyOrderModel({
//     required this.id,
//     required this.title,
//     required this.status,
//   });
// }

class MyOrderModel {
  final int id;
  final String mainCategoryTitle;
  final String subCategoryTitle;
  final String description;
  final String location;

  MyOrderModel({
    required this.id,
    required this.mainCategoryTitle,
    required this.subCategoryTitle,
    required this.description,
    required this.location,
  });

  factory MyOrderModel.fromJson(Map<String, dynamic> json) {
    return MyOrderModel(
      id: json['id'] ?? 0,
      mainCategoryTitle: json['mainCategory']?['title'] ?? '',
      subCategoryTitle: json['category']?['title'] ?? '',
      description: json['description'] ?? '',
      location: json['address']?['title'] ?? '',
    );
  }
  @override
  String toString() {
    return 'Offer(id: $id, desc: $description,)';
  }
}
