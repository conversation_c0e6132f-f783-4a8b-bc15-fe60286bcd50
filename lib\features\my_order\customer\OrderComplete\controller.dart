import 'package:dio/dio.dart';
import 'package:get/get.dart' hide FormData;
import 'package:renvo_app/core/services/pagination/controller.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/features/my_order/controller.dart';
import 'package:renvo_app/features/my_order/models/my_orders.dart';

class OrderCompleteController extends GetxController {
  // var orders = <MyOrderModel>[].obs;
  var reviews = <int, Map<String, dynamic>>{}.obs;
  // @override
  // void onInit() {
  //   super.onInit();
  //   fetchPendingOrders();
  // }

  // void fetchPendingOrders() async {
  //   await Future.delayed(Duration(seconds: 1)); // simulation
  //   orders.value = List.generate(
  //     5,
  //     (i) => MyOrderModel(
  //         id: i, title: "Pending Order ${i + 1}", status: OrderStatus.pending),
  //   );
  // }
  Future<void> submitReview(
    int orderId,
    double rate,
    String review,
  ) async {
    final response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.rating,
        method: RequestMethod.Post,
        body: FormData.fromMap({
          "order_id": orderId,
          "rate": rate,
          "review": review,
        }),
        copyHeader: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
      ),
    );
    Get.back();
    if (response.success) {
      // pending_controller.pagerController.refreshData();

      // progress_controller.pagerController.refreshData();

      Get.snackbar("Success", "${response.data}");
    } else {
      Get.snackbar("Error", "${response.data}");
    }

    // return response;
  }

  late PaginationController pagerController;

  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.request(
      Request(
        endPoint: EndPoints.customer_orders,
        params: {"page": page, "status": "completed"},
        cancelToken: cancel,
      ),
    );
    return response;
  }

  refreshData() {
    pagerController.refreshData();
  }
}
