import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:renvo_app/core/services/pagination/controller.dart';
import 'package:renvo_app/core/services/rest_api/rest_api.dart';
import 'package:renvo_app/core/services/state_management/obs.dart';
import 'package:renvo_app/features/my_order/models/my_offer.dart';
import 'package:renvo_app/features/my_order/models/view_offer.dart';

class ViewOffersPageController extends GetxController {
  // ObsList<VOfferModel> offers = ObsList([]);
  late PaginationController pagerController;

  @override
  void onInit() {
    super.onInit();
  }

  Future<ResponseModel> Function(int, CancelToken) viewOffer(int id) {
    return (int page, CancelToken cancel) async {
      final response = await APIService.instance.request(
        Request(
          endPoint: EndPoints.order_offers(id),
          params: {
            "page": page,
          },
          cancelToken: cancel,
          copyHeader: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          },
        ),
      );
      return response;
    };
  }

  @override
  void onClose() {
    super.onClose();
  }
}
