// ignore_for_file: constant_identifier_names, non_constant_identifier_names

abstract class EndPoints {
  //##########  Base Url  ##########
  static const String baseUrl =
      // 'https://b4f84ebd-016e-4fed-9492-9e3bd664d12b.mock.pstmn.io/';
      'http://94.72.98.154/renva/public/api/';

  //Auth
  static const login = "v1/login";
  static const register = "v1/register";
  static const get_profile = "v1/profile";
  // Content
  static const categories = "categories";
  static const products = "products";
  static const provider_categories = "v1/provider_categories";
  static sub_categories(int id) => "v1/sub_categories/$id";
  static const orders = "v1/orders";
  static const all_orders_list = "v1/orders_by_status";
  static const new_offer = "v1/offers";
  static const customer_orders = "v1/customer/orders";
  static view_c_offer(int id) => "v1/offers/$id";
  static const provider_orders = "v1/provider_orders";
  static order_offers(int id) => "v1/order/offers/$id";
  static const accept_offers = "v1/offers/accept";
  static const end_order = "v1/orders/provider/end";
  static const rating = "v1/orders/customer/review";

  // static view_c_offer(int id) => " v1/order/offers/$id";
}
